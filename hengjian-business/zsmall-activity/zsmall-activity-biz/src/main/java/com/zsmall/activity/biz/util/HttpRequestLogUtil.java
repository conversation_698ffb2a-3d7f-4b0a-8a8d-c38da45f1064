package com.zsmall.activity.biz.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * HTTP请求日志记录工具类
 * <AUTHOR>
 */
@Slf4j
public class HttpRequestLogUtil {

    /**
     * 记录完整的HTTP请求信息
     * @param logPrefix 日志前缀
     * @param method 请求方法
     * @param url 请求地址
     * @param headers 请求头
     * @param body 请求体
     * @param timeoutMs 超时时间(毫秒)
     */
    public static void logCompleteRequestInfo(String logPrefix, String method, String url, 
                                            Map<String, String> headers, String body, Integer timeoutMs) {
        StringBuilder requestInfo = new StringBuilder();
        requestInfo.append("\n").append("=".repeat(100)).append("\n");
        requestInfo.append(logPrefix).append("完整请求信息：").append("\n");
        requestInfo.append("=".repeat(100)).append("\n");
        requestInfo.append("请求方法：").append(method).append("\n");
        requestInfo.append("请求地址：").append(url).append("\n");
        
        // 记录请求头
        if (headers != null && !headers.isEmpty()) {
            requestInfo.append("请求头：").append("\n");
            headers.forEach((key, value) -> {
                // 对敏感信息进行脱敏处理
                String displayValue = maskSensitiveInfo(key, value);
                requestInfo.append("  ").append(key).append(": ").append(displayValue).append("\n");
            });
        }
        
        if (timeoutMs != null) {
            requestInfo.append("超时时间：").append(timeoutMs).append("ms").append("\n");
        }
        
        requestInfo.append("请求体：").append("\n");
        
        // 格式化请求体，使其更易读
        if (StrUtil.isNotBlank(body)) {
            try {
                // 尝试格式化JSON
                if (body.trim().startsWith("{") || body.trim().startsWith("[")) {
                    requestInfo.append(JSONUtil.formatJsonStr(body));
                } else {
                    requestInfo.append(body);
                }
            } catch (Exception e) {
                // 格式化失败，直接输出原始内容
                requestInfo.append(body);
            }
        } else {
            requestInfo.append("(空)");
        }
        
        requestInfo.append("\n").append("=".repeat(100));
        
        log.error(requestInfo.toString());
    }

    /**
     * 记录HTTP响应信息
     * @param logPrefix 日志前缀
     * @param statusCode 状态码
     * @param responseBody 响应体
     * @param costTimeMs 耗时(毫秒)
     */
    public static void logCompleteResponseInfo(String logPrefix, Integer statusCode, 
                                             String responseBody, Long costTimeMs) {
        StringBuilder responseInfo = new StringBuilder();
        responseInfo.append("\n").append("-".repeat(100)).append("\n");
        responseInfo.append(logPrefix).append("完整响应信息：").append("\n");
        responseInfo.append("-".repeat(100)).append("\n");
        
        if (statusCode != null) {
            responseInfo.append("状态码：").append(statusCode).append("\n");
        }
        
        if (costTimeMs != null) {
            responseInfo.append("耗时：").append(costTimeMs).append("ms").append("\n");
        }
        
        responseInfo.append("响应体：").append("\n");
        
        // 格式化响应体
        if (StrUtil.isNotBlank(responseBody)) {
            try {
                // 尝试格式化JSON
                if (responseBody.trim().startsWith("{") || responseBody.trim().startsWith("[")) {
                    responseInfo.append(JSONUtil.formatJsonStr(responseBody));
                } else {
                    responseInfo.append(responseBody);
                }
            } catch (Exception e) {
                // 格式化失败，直接输出原始内容
                responseInfo.append(responseBody);
            }
        } else {
            responseInfo.append("(空)");
        }
        
        responseInfo.append("\n").append("-".repeat(100));
        
        log.error(responseInfo.toString());
    }

    /**
     * 对敏感信息进行脱敏处理
     * @param key 键名
     * @param value 值
     * @return 脱敏后的值
     */
    private static String maskSensitiveInfo(String key, String value) {
        if (StrUtil.isBlank(value)) {
            return value;
        }
        
        String lowerKey = key.toLowerCase();
        
        // 对Authorization、Token等敏感信息进行脱敏
        if (lowerKey.contains("authorization") || lowerKey.contains("token") || 
            lowerKey.contains("password") || lowerKey.contains("secret")) {
            
            if (value.length() <= 8) {
                return "*".repeat(value.length());
            } else {
                // 显示前4位和后4位，中间用*代替
                return value.substring(0, 4) + "*".repeat(value.length() - 8) + value.substring(value.length() - 4);
            }
        }
        
        return value;
    }

    /**
     * 简化版本的请求日志记录（兼容原有代码）
     * @param logPrefix 日志前缀
     * @param url 请求地址
     * @param headerApiKey 请求头Authorization值
     * @param jsonBody 请求体
     */
    public static void logSimpleRequestInfo(String logPrefix, String url, String headerApiKey, String jsonBody) {
        Map<String, String> headers = Map.of(
            "Authorization", headerApiKey,
            "Content-Type", "application/json"
        );
        logCompleteRequestInfo(logPrefix, "POST", url, headers, jsonBody, 10000);
    }
}
