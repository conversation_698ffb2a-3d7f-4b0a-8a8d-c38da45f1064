package com.zsmall.activity.biz.example;

import cn.hutool.http.HttpRequest;
import com.zsmall.activity.biz.util.HttpRequestLogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求日志记录使用示例
 * <AUTHOR>
 */
@Slf4j
public class HttpRequestLogExample {

    /**
     * 示例：如何使用HttpRequestLogUtil记录完整的HTTP请求信息
     */
    public void exampleUsage() {
        String logPrefix = "[示例接口调用]";
        String url = "https://api.example.com/test";
        String requestBody = "{\"name\":\"test\",\"value\":123}";
        
        // 准备请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer your_token_here");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "MyApp/1.0");
        
        // 记录请求信息
        HttpRequestLogUtil.logCompleteRequestInfo(
            logPrefix, 
            "POST", 
            url, 
            headers, 
            requestBody, 
            5000
        );
        
        // 执行HTTP请求
        long startTime = System.currentTimeMillis();
        try {
            String response = HttpRequest.post(url)
                .headerMap(headers, true)
                .body(requestBody)
                .timeout(5000)
                .execute()
                .body();
            
            long costTime = System.currentTimeMillis() - startTime;
            
            // 记录响应信息
            HttpRequestLogUtil.logCompleteResponseInfo(
                logPrefix, 
                200, 
                response, 
                costTime
            );
            
        } catch (Exception e) {
            long costTime = System.currentTimeMillis() - startTime;
            
            // 记录错误响应
            HttpRequestLogUtil.logCompleteResponseInfo(
                logPrefix, 
                null, 
                "请求异常: " + e.getMessage(), 
                costTime
            );
            
            log.error(logPrefix + "请求执行失败", e);
        }
    }

    /**
     * 简化版本的使用示例（兼容原有代码风格）
     */
    public void simpleUsageExample() {
        String logPrefix = "[ERP库存操作]";
        String url = "https://erp.example.com/inventory/lock";
        String token = "Bearer your_api_token";
        String jsonBody = "{\"orgWarehouseCode\":\"WH001\",\"inventoryType\":0}";
        
        // 使用简化版本的日志记录
        HttpRequestLogUtil.logSimpleRequestInfo(logPrefix, url, token, jsonBody);
        
        // 执行请求...
        // String response = HttpRequest.post(url)...
        
        // 记录响应
        String mockResponse = "{\"code\":200,\"message\":\"success\"}";
        HttpRequestLogUtil.logCompleteResponseInfo(logPrefix, 200, mockResponse, 1500L);
    }
}
